---
inclusion: always
---

# Project Structure Guidelines

## Core Architecture Rules

### Configuration Management
- **Never hardcode values** - all parameters must be in `config.py`
- Import pattern: `from config import CONFIG`
- Access pattern: `CONFIG.TRADING.INITIAL_CASH`, `CONFIG.DATA.ETF_SYMBOLS`
- No additional config files - keep everything centralized

### File Organization
- **`main.py`**: Single entry point with command-line interface - contains all system logic
- **`config.py`**: All configuration parameters organized by category
- **`data/`**: Market data cache (auto-created)
- **`models/`**: Trained PPO models with metadata (used by evaluation mode)
- **`logs/`**: System logs with timestamp format
- **`results/`**: Performance outputs and analysis (CSV files generated in evaluation mode)

### Naming Conventions
- **Log files**: `rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- **Model files**: Include training date and performance metadata
- **Result files**: JSON summaries and CSV histories with timestamps
- **No custom directories** - use existing structure only

## Code Organization Patterns

### Command-Line Interface Architecture
- **Argument parsing**: Use `argparse` for mode selection and configuration
- **Mode validation**: Validate execution requirements before starting
- **Mode routing**: Dispatch to appropriate execution handlers based on mode
- **Data separation**: Implement temporal splitting for training/evaluation modes
- **Output control**: Generate mode-specific outputs (CSV for evaluation, models for training)

### TensorTrade Integration
- Use component registration with proper names
- Follow TensorTrade's factory pattern for environment creation
- Implement authentic components - no mock or simplified versions
- All instruments must have `id` attribute set to symbol

### Data Flow Architecture
- **Daily data fetching**: Yahoo Finance daily data for ETFs and ^TNX → preprocessing → TensorTrade streams
- 4-year sliding windows (not expanding) using daily data
- **Data splitting**: 80/20 temporal split for training/evaluation modes
- **Monthly execution**: Program runs at end of each month for portfolio composition results
- Monthly rebalancing frequency only
- Technical indicators via `ta` library integration

### Error Handling Standards
- Fix root causes - don't catch and ignore
- Provide actionable error messages
- Validate component integration before execution
- Include full stack traces in logs for debugging