---
inclusion: always
---

# Technology Stack & Development Guidelines

## Core Dependencies

### Required Libraries
- **TensorTrade**: Install from GitHub (`pip install git+https://github.com/tensortrade-org/tensortrade.git`)
- **stable-baselines3** (>=2.0.0): PPO algorithm implementation
- **yfinance** (>=0.2.0): Yahoo Finance data fetching
- **pandas/numpy**: Data manipulation and numerical computing
- **ta** (>=0.10.0): Technical analysis indicators
- **argparse**: Command-line argument parsing (built-in Python module)

### Installation Commands
```bash
# Automatic installation (preferred)
python install_dependencies.py

# Manual fallback
pip install -r requirements.txt
pip install git+https://github.com/tensortrade-org/tensortrade.git
```

## Development Rules

### Code Quality Standards
- **No extra files**: Only create files when absolutely necessary
- **No fallback mechanisms**: Fix root causes, don't work around issues
- **Use authentic components**: Always use real TensorTrade classes, never mock implementations
- **Configuration-driven**: All parameters must be in `config.py`, never hardcode values

### Import Patterns
```python
from config import CONFIG
# Access: CONFIG.TRADING.INITIAL_CASH, CONFIG.DATA.ETF_SYMBOLS
```

### TensorTrade Integration Requirements
- All instruments must have `id` attribute set to their symbol
- Use TensorTrade's Portfolio class (not custom implementations)
- Register ActionScheme and RewardScheme components with proper names
- Implement real trading mechanics with transaction costs and slippage

### Data Pipeline Architecture
- **Daily data fetching**: ETF and ^TNX data via yfinance for model training
- **4-year sliding windows**: Use daily data, not monthly aggregated
- **Data separation**: 80/20 temporal split for training/evaluation modes
- **Monthly execution**: Program runs at month-end for portfolio composition
- Technical indicators via `ta` library integration

### Command-Line Interface Requirements
- **Mode selection**: Support `--mode={training,evaluation,full}` arguments
- **Model management**: Support `--model-path` for custom model locations
- **Argument validation**: Validate mode requirements before execution
- **Error handling**: Provide clear error messages for invalid arguments
- **Backward compatibility**: Default behavior maintains existing functionality

### Error Handling Standards
- Fix underlying issues rather than catching and ignoring errors
- Provide actionable error messages with full context
- Validate component integration before execution
- Include complete stack traces in logs for debugging

## File Structure
- **Entry point**: `main.py` (single file with command-line interface contains all system logic)
- **Configuration**: `config.py` (centralized parameters only)
- **Dependencies**: `requirements.txt` + `install_dependencies.py`
- **Auto-created directories**: `data/`, `models/`, `logs/`, `results/`

## Command-Line Usage Patterns
```bash
# Training mode only
python main.py --mode=training

# Evaluation mode only (requires existing model)
python main.py --mode=evaluation

# Full mode (default - backward compatible)
python main.py
python main.py --mode=full

# Custom model path for evaluation
python main.py --mode=evaluation --model-path=/path/to/model.zip
```